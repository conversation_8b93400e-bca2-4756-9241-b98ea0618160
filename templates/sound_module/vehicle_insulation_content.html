<!-- 车型隔声量对比页面内容 - 用于AJAX加载 -->
<!-- 查询条件 -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-filter me-2"></i>查询条件
        </h5>
    </div>
    <div class="card-body">
        <form id="query-form">
            <div class="row g-3 align-items-start">
                <div class="col-md-6">
                    <div class="vehicle-multiselect" id="vehicle-multiselect">
                        <div class="multiselect-container">
                            <div class="multiselect-input-container">
                                <input type="text" class="form-control multiselect-input" placeholder="点击选择车型..." readonly>
                                <i class="fas fa-chevron-down multiselect-arrow"></i>
                            </div>
                            <div class="multiselect-dropdown">
                                <div class="multiselect-search">
                                    <input type="text" class="form-control form-control-sm" placeholder="搜索车型...">
                                </div>
                                <div class="multiselect-options">
                                    <!-- 动态加载选项 -->
                                </div>
                            </div>
                        </div>
                        <div class="selected-items mt-2">
                            <!-- 已选择的车型标签 -->
                        </div>
                    </div>
                </div>
                <div class="col-md-6 d-flex align-items-start">
                    <button type="button" class="btn btn-primary" id="generate-btn" disabled>
                        <i class="fas fa-chart-line me-1"></i>生成对比表
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 结果展示区域 -->
<div class="card" id="results-card" style="display: none;">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-table me-2"></i>车型隔声量对比结果
        </h5>
        <div class="d-flex align-items-center gap-3">
            <span id="vehicle-count" class="badge bg-secondary">0 个车型</span>
            <button type="button" class="btn btn-outline-success btn-sm" id="export-btn">
                <i class="fas fa-download me-1"></i>导出数据
            </button>
        </div>
    </div>
    <div class="card-body">
        <!-- 数据表格 -->
        <div class="table-responsive mb-4">
            <table class="table table-striped table-hover" id="comparison-table">
                <thead class="table-dark">
                    <!-- 动态生成表头 -->
                </thead>
                <tbody>
                    <!-- 动态生成表格内容 -->
                </tbody>
            </table>
        </div>
        
        <!-- 折线对比图 -->
        <div class="mb-4">
            <h6><i class="fas fa-chart-line me-2"></i>折线对比图</h6>
            <div id="chart-container" style="width: 100%; height: 400px;"></div>
        </div>
        
        <!-- 测试信息 -->
        <div class="mt-4">
            <h6><i class="fas fa-info-circle me-2"></i>测试信息</h6>
            <div class="table-responsive">
                <table class="table table-sm table-bordered" id="test-info-table">
                    <thead class="table-light">
                        <tr>
                            <th>车型</th>
                            <th>测试日期</th>
                            <th>测试工程师</th>
                            <th>测试地点</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 动态生成测试信息 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- 空状态提示 -->
<div class="card" id="empty-state" style="display: block;">
    <div class="card-body text-center py-5">
        <i class="fas fa-car fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">请选择车型生成对比表</h5>
        <p class="text-muted">选择一个或多个车型，点击"生成对比表"按钮查看隔声量对比数据</p>
    </div>
</div>

<!-- 加载提示 -->
<div class="text-center py-4" id="loading-indicator" style="display: none;">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
    </div>
    <p class="mt-2 text-muted">正在生成对比数据...</p>
</div>

<!-- 测试图片模态框 -->
<div class="modal fade" id="testImageModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-image me-2"></i>测试图片
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <div id="image-loading" class="py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2 text-muted">正在加载图片...</p>
                </div>
                <div id="image-content" style="display: none;">
                    <img id="test-image" class="img-fluid" style="max-height: 70vh;" alt="测试图片">
                </div>
                <div id="image-error" class="alert alert-warning" style="display: none;">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    图片加载失败或不存在
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 错误提示 -->
<div class="alert alert-danger" id="error-alert" style="display: none;">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <span id="error-message"></span>
</div>
